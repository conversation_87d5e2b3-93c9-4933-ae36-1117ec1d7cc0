import { getPrisma } from "@udoy/utils/db-utils";
import { SummaryType } from "@prisma/client";
import {
  startOfDay,
  endOfDay,
  startOfWeek,
  endOfWeek,
  startOfMonth,
  endOfMonth,
  startOfYear,
  endOfYear,
  subDays,
  subWeeks,
  subMonths,
  subYears,
  format
} from "date-fns";
import { SummaryChartData } from "./SummaryOverviewChart";

type TimeRange = "daily" | "weekly" | "monthly" | "yearly";

/**
 * Gets summary data for the specified time range
 * Note: This function assumes authentication is handled at the component level
 */
export async function getSummaryData(timeRange: TimeRange): Promise<SummaryChartData[]> {
  const prisma = getPrisma();

    let summaryType: SummaryType;
    let periods: { start: Date; end: Date; name: string }[] = [];
    const now = new Date();

    switch (timeRange) {
      case "daily":
        summaryType = SummaryType.DAILY;
        // Get last 30 days
        for (let i = 29; i >= 0; i--) {
          const date = subDays(now, i);
          periods.push({
            start: startOfDay(date),
            end: endOfDay(date),
            name: format(date, "MMM d"),
          });
        }
        break;

      case "weekly":
        summaryType = SummaryType.WEEKLY;
        // Get last 12 weeks
        for (let i = 11; i >= 0; i--) {
          const date = subWeeks(now, i);
          periods.push({
            start: startOfWeek(date),
            end: endOfWeek(date),
            name: format(date, "MMM d"),
          });
        }
        break;

      case "yearly":
        summaryType = SummaryType.YEARLY;
        // Get last 5 years
        for (let i = 4; i >= 0; i--) {
          const date = subYears(now, i);
          periods.push({
            start: startOfYear(date),
            end: endOfYear(date),
            name: format(date, "yyyy"),
          });
        }
        break;

      case "monthly":
      default:
        summaryType = SummaryType.MONTHLY;
        // Get last 12 months
        for (let i = 11; i >= 0; i--) {
          const date = subMonths(now, i);
          periods.push({
            start: startOfMonth(date),
            end: endOfMonth(date),
            name: format(date, "MMM yyyy"),
          });
        }
        break;
    }

    // Fetch summaries for each period
    const data = await Promise.all(
      periods.map(async (period) => {
        const summaries = await prisma.summary.findMany({
          where: {
            type: summaryType,
            startDate: {
              gte: period.start,
              lte: period.end,
            },
          },
        });

        // Aggregate data for the period
        const totalRevenue = summaries.reduce((sum, s) => sum + s.totalRevenue, 0);
        const totalProfit = summaries.reduce((sum, s) => sum + s.totalProfit, 0);
        const totalOrders = summaries.reduce((sum, s) => sum + s.totalOrders, 0);

        return {
          name: period.name,
          revenue: totalRevenue,
          profit: totalProfit,
          orders: totalOrders,
          period: period.name,
        };
      })
    );

    return data;
}
