"use client";

import { useState, useTransition } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import { SummaryOverviewChart, SummaryChartData } from "./SummaryOverviewChart";
import { getSummaryData } from "./useSummaryData";

type TimeRange = "daily" | "weekly" | "monthly" | "yearly";

interface SummaryOverviewClientProps {
  initialData: SummaryChartData[];
  initialTimeRange: TimeRange;
}

export function SummaryOverviewClient({ 
  initialData, 
  initialTimeRange 
}: SummaryOverviewClientProps) {
  const [timeRange, setTimeRange] = useState<TimeRange>(initialTimeRange);
  const [data, setData] = useState<SummaryChartData[]>(initialData);
  const [error, setError] = useState<string | null>(null);
  const [isPending, startTransition] = useTransition();

  const handleTimeRangeChange = (newTimeRange: TimeRange) => {
    setTimeRange(newTimeRange);
    setError(null);
    
    startTransition(async () => {
      try {
        const newData = await getSummaryData(newTimeRange);
        setData(newData);
      } catch (err) {
        console.error("Error fetching summary data:", err);
        setError("Failed to load data for the selected time range");
      }
    });
  };

  return (
    <Card className="col-span-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Summary Overview</CardTitle>
            <CardDescription>
              View your business performance based on summary data
            </CardDescription>
          </div>
          <Select
            value={timeRange}
            onValueChange={handleTimeRangeChange}
            disabled={isPending}
          >
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">Daily</SelectItem>
              <SelectItem value="weekly">Weekly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="yearly">Yearly</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent className="pl-2">
        {isPending ? (
          <div className="flex items-center justify-center h-[350px]">
            <div className="text-muted-foreground">Loading summary data...</div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-[350px]">
            <div className="text-destructive">Error loading data: {error}</div>
          </div>
        ) : data.length === 0 ? (
          <div className="flex items-center justify-center h-[350px]">
            <div className="text-muted-foreground">
              No summary data available for {timeRange} view
            </div>
          </div>
        ) : (
          <SummaryOverviewChart data={data} timeRange={timeRange} />
        )}
      </CardContent>
    </Card>
  );
}
