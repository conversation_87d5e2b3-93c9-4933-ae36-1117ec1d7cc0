import { Suspense } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import { SummaryOverviewClient } from "./SummaryOverviewClient";
import { getSummaryData } from "./useSummaryData";
import {
  unstable_cacheTag as cacheTag,
  unstable_cacheLife as cacheLife,
} from "next/cache";
import { CacheKey } from "@udoy/utils/cache-key";

type TimeRange = "daily" | "weekly" | "monthly" | "yearly";

interface SummaryOverviewProps {
  timeRange?: TimeRange;
}

async function SummaryOverviewContent({ timeRange = "daily" }: SummaryOverviewProps) {
  "use cache";
  cacheTag(CacheKey.DashboardStats(`summary-data-${timeRange}`));
  cacheLife("hours");

  const data = await getSummaryData(timeRange);

  return (
    <SummaryOverviewClient
      initialData={data}
      initialTimeRange={timeRange}
    />
  );
}

function SummaryOverviewLoading() {
  return (
    <Card className="col-span-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Summary Overview</CardTitle>
            <CardDescription>
              View your business performance based on summary data
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pl-2">
        <div className="flex items-center justify-center h-[350px]">
          <div className="text-muted-foreground">Loading summary data...</div>
        </div>
      </CardContent>
    </Card>
  );
}

export function SummaryOverview({ timeRange }: SummaryOverviewProps) {
  return (
    <Suspense fallback={<SummaryOverviewLoading />}>
      <SummaryOverviewContent timeRange={timeRange} />
    </Suspense>
  );
}
